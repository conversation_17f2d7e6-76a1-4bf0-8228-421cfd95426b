using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.UI;
public class MainMenu : MonoBehaviour
{
    public GameObject modepnl, levlpanl, lodingpanl, menupanl, exitpnl, setingpnl, selectbtn, levlmode2, lv2bacpnl;
    public GameObject[] LevelButton, Level1,level2;
    public static int levlno;
    public AudioSource BtnSound, Sound;
    public Slider music , sound;
    public Text coins;
    void Start()
    {
        Time.timeScale = 1;
        coins.text = PlayerPrefs.GetInt("coins").ToString();
        for (int i = 0; i < LevelButton.Length; i++)
        {
            if (PlayerPrefs.GetInt("Level" + i) == 1)
            {
                LevelButton[i + 1].transform.GetChild(0).gameObject.SetActive(false);
                LevelButton[i + 1].transform.GetComponent<Button>().interactable = true;
            }
        }
        for (int i = 0; i < Level1.Length; i++)
        {
            if (PlayerPrefs.GetInt("Lvl" + i) == 1)
            {
                Level1[i + 1].transform.GetChild(0).gameObject.SetActive(false);
            }
        }
        for (int i = 0; i < level2.Length; i++)
        {
            if (PlayerPrefs.GetInt("Lvll" + i) == 1)
            {
                level2[i + 1].transform.GetChild(0).gameObject.SetActive(false);
            }
        }
       
    }
    public void play()
    {
        menupanl.SetActive(false);
        lodingpanl.SetActive(true);
        StartCoroutine("lod2");
       
    }
    public void exit()
    {
        menupanl.SetActive(false);
        exitpnl.SetActive(true);
        
    }
    public void No()
    {
        menupanl.SetActive(true);
        exitpnl.SetActive(false);
        
    }
    public void modeback()
    {
        menupanl.SetActive(true);
        modepnl.SetActive(false);
    }
    public void lvlback()
    {
        levlpanl.SetActive(false);
        modepnl.SetActive(true);
    }
    public void lvl2back()
    {
        lv2bacpnl.SetActive(false);
        modepnl.SetActive(true);
    }
    public void setting()
    {
        menupanl.SetActive(false);
        setingpnl.SetActive(true);
       
    }
    public void save()
    {
        menupanl.SetActive(true);
        setingpnl.SetActive(false);
      
    }
    public void yes()
    {
        Application.Quit();
    }
    public void Levels(int levl)
    {
        levlno = levl;
         lodingpanl.SetActive(true);
        StartCoroutine(loading());
      
        
    }
    public void fill()
    {
        music.value += 0.1f;
        BtnSound.volume = music.value;
    }
    public void Lvel(int lvl)
    {
        levlno = lvl;
        lodingpanl.SetActive(true);
       StartCoroutine("farmmod");
      
    }
    IEnumerator farmmod()
    {
        yield return new WaitForSeconds(4f);
        SceneManager.LoadScene("Farming mod");
    }
    public void levelselect2(int lvl2)
    {
        levlno = lvl2;
        lv2bacpnl.SetActive(false);
        lodingpanl.SetActive(true);
    }
    public void select()
    {

        StartCoroutine("loading");

    }
    public void caremode()
    {
        lodingpanl.SetActive(true);
        StartCoroutine("load");
      
    }
    public void mode2()
    {
        lodingpanl.SetActive(true);
        StartCoroutine("load1");
       
    }
    public void filldec()
    {
        fil.fillAmount -= 0.1f;
        BtnSound.volume = fil.fillAmount;
    }
    public void fl()
    {
        fel.fillAmount += 0.1f;
        Sound.volume = fil.fillAmount;
    }
    public void fldec()
    {
        fel.fillAmount -= 0.1f;
        Sound.volume = fil.fillAmount;
    }
    public void Steer()
    {
        RCC.SetMobileController(RCC_Settings.MobileController.SteeringWheel);
    }
    public void Btns()
    {
        RCC.SetMobileController(RCC_Settings.MobileController.TouchScreen);
    }
    public void Tilt()
    {
        RCC.SetMobileController(RCC_Settings.MobileController.Gyro);
    }
    IEnumerator loading()
    {
        yield return new WaitForSeconds(1f);
       
        yield return new WaitForSeconds(3f);
       
        SceneManager.LoadScene("gameplay");
    }
    public void rateus()
    {
        Application.OpenURL("https://play.google.com/store/apps/details?id=com.smg.tractor.trolly.games.farming.game");
    }
    public void privacy()
    {
        Application.OpenURL("https://simulatorgames2022.blogspot.com/2023/04/privacy-policy.html");
    }
    public void moregams()
    {
        Application.OpenURL("https://play.google.com/store/apps/dev?id=6151632225219809775");
    }
    public void mudjeep()
    {
        Application.OpenURL("https://play.google.com/store/apps/details?id=com.smg.offroadjeep.mudjeep.offtheroad.jeepgame.simulator.offroad.driving.games");
    }
    public void trainadd()
    {
        Application.OpenURL("https://play.google.com/store/apps/details?id=com.smg.city.train.simulator.zt.game&hl=en");
    }
    IEnumerator loding()
    {
        yield return new WaitForSeconds(1f);
       
        yield return new WaitForSeconds(3f);
       
        SceneManager.LoadScene("tractortochan");
    }
    IEnumerator load()
    {
       
        yield return new WaitForSeconds(3f);
        
        levlpanl.SetActive(true);
        lodingpanl.SetActive(false);
    }
    IEnumerator load1()
    {
     
        yield return new WaitForSeconds(3f);
      
        levlmode2.SetActive(true);
        lodingpanl.SetActive(false);
    }
    IEnumerator lod2()
    {
         yield return new WaitForSeconds(1f);
     
        yield return new WaitForSeconds(2f);
       
        modepnl.SetActive(true);
        lodingpanl.SetActive(false);
    }
}
